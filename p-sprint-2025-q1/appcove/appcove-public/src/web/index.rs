#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("AppCove");

        doc.add_body(html!(
            div.container.bg-white {
                h1 { "Welcome to AppCove" }

                p { "This is the AppCove application homepage." }

                hr;

                .row {
                    .col-md-6 {
                        h2 { "Features" }
                        ul {
                            li { "Application Management" }
                            li { "User Management" }
                            li { "Team Collaboration" }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}