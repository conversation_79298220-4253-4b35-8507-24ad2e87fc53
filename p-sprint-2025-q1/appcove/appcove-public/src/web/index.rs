#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("AppCove CRM");

        doc.add_body(html!(
            div.container.bg-white {
                h1 { "Welcome to AppCove CRM" }

                p { "This is the AppCove CRM application homepage." }

                hr;

                .row {
                    .col-md-6 {
                        h2 { "Features" }
                        ul {
                            li { "Customer Management" }
                            li { "Lead Tracking" }
                            li { "Sales Pipeline" }
                            li { "Contact Management" }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
