#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("AppCove");

        doc.add_body(html!(
            div.d-flex.vh-100 {
                // Left Sidebar
                div.bg-dark.text-white style="width: 250px; min-height: 100vh;" {
                    div.p-4 {
                        // Logo
                        div.mb-4 {
                            div.rounded-circle.d-inline-block style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);" {
                            }
                        }

                        // Navigation
                        nav {
                            ul.nav.flex-column {
                                li.nav-item {
                                    a.nav-link.text-white href="/dashboard/" { "Dashboard" }
                                }
                                li.nav-item {
                                    a.nav-link.text-white href="/about/" { "About" }
                                }
                            }
                        }
                    }
                }

                // Main Content Area
                div.flex-grow-1.bg-light {
                    // Top Navigation
                    nav.navbar.navbar-expand-lg.navbar-light.bg-white.border-bottom {
                        div.container-fluid {
                            div.ms-auto {
                                a.btn.btn-outline-secondary.me-2 href="/dashboard/" { "Dashboard" }
                                a.btn.btn-outline-secondary.me-2 href="/about/" { "About" }
                                a.btn.btn-primary href="/auth/login/" { "Sign In" }
                            }
                        }
                    }

                    // Breadcrumb
                    nav.bg-white.border-bottom {
                        div.container-fluid.py-2 {
                            ol.breadcrumb.mb-0 {
                                li.breadcrumb-item {
                                    a href="/dashboard/" { "Dashboard" }
                                }
                                li.breadcrumb-item {
                                    a href="/about/" { "About" }
                                }
                            }
                        }
                    }

                    // Main Content
                    div.d-flex.align-items-center.justify-content-center style="min-height: calc(100vh - 120px);" {
                        div.card.shadow style="width: 400px;" {
                            div.card-body.p-5 {
                                h3.text-center.mb-4 { "Login" }

                                // Google Login Button
                                div.d-grid.mb-3 {
                                    a.btn.btn-outline-dark.btn-lg href="/auth/google/" {
                                        i.fab.fa-google.me-2 {}
                                        "Google"
                                    }
                                }

                                div.text-center.text-muted.mb-3 { "or" }

                                // Username/Password Login Link
                                div.text-center {
                                    a.text-primary href="/auth/login/" { "Login with username and password" }
                                }
                            }
                        }
                    }

                    // Footer
                    footer.bg-white.border-top.mt-auto {
                        div.container-fluid.py-3 {
                            div.text-center.text-muted.small {
                                "Copyright © 2023 AppCove, Inc. All rights reserved."
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
