#[approck::http(GET /dashboard/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Dashboard");

        doc.add_body(html! {
            div.container.mt-4 {
                div.row {
                    div.col-12 {
                        h1 { "Dashboard" }
                        p { "Welcome to AppCove CRM" }
                        
                        div.row.mt-4 {
                            div.col-md-4 {
                                div.card {
                                    div.card-body {
                                        h5.card-title { "Customers" }
                                        p.card-text { "Manage your customer database" }
                                        a.btn.btn-primary href="/customers/" { "View Customers" }
                                    }
                                }
                            }
                            div.col-md-4 {
                                div.card {
                                    div.card-body {
                                        h5.card-title { "Leads" }
                                        p.card-text { "Track and manage leads" }
                                        a.btn.btn-primary href="/leads/" { "View Leads" }
                                    }
                                }
                            }
                            div.col-md-4 {
                                div.card {
                                    div.card-body {
                                        h5.card-title { "Reports" }
                                        p.card-text { "View sales reports and analytics" }
                                        a.btn.btn-primary href="/reports/" { "View Reports" }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });

        Response::HTML(doc.into())
    }
}
