bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            _app: &'static crate::AppStruct,
            _identity: &crate::IdentityStruct,
            _req: &approck::server::Request,
        ) -> Self {
            use bux::document::<PERSON><PERSON>;
            use bux::document::{Base, Nav2};

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(_req.path());
            this.set_title("AppCove CRM"); // default title
            this.set_site_name("AppCove CRM");
            this.set_owner("AppCove, Inc.");

            // Nav2 setup
            this.set_identity(_identity);

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }

    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl bux::document::Cliffy for Document {}
    impl auth_fence::Document for Document {}
}
