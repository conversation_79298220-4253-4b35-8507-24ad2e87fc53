[package]
name = "appcove-crm"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.acp]
app.port = 3020

extends = ["appcove-crm-zero", "appcove-crm-public", "approck", "bux", "granite", "auth-fence"]

[dependencies]
appcove-crm-zero = { path = "../appcove-crm-zero" }
appcove-crm-public = { path = "../appcove-crm-public" }
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
